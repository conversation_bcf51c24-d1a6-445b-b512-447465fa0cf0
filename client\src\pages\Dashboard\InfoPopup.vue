<template>
  <Modal
    title="Perhatian!"
    :show.sync="xshow"
    width="400px"
    cancelText="OK"
    submitText=""
  >
    <div style="width: 400px">
      Kami informasikan kepada pengguna bahwa terdapat indikasi akses tidak sah
      terhadap sistem elektronik.
      <br />
      <br />
      Saat ini sistem elektronik dalam keadaan normal dan tetap dapat melayani
      anda sebagaimana mestinya. Kami juga telah melakukan langkah-langkah untuk
      meningkatkan keamanan sebagai bentuk komitmen kami dalam menjaga
      integritas informasi Anda.
      <br />
      <br />
      <PERSON><PERSON><PERSON>, kami juga menghimbau agar Anda selalu melakukan penggantian kata
      sandi secara berkala untuk mencegah potensi penyalahgunaan data.
      Terimakasih.
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
  }),
  props: {
    show: <PERSON><PERSON><PERSON>,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      //   let ret = await this.api.call('', {})
      //   if (ret.success) this.emit('update:show', false)
    },
  },
}
</script>
