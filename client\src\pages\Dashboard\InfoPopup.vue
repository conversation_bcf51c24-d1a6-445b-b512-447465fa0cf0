<template>
  <Modal
    title="Perhatian!"
    :show.sync="xshow"
    width="400px"
    cancelText="OK"
    submitText="DAFTARKAN BIOMETRIK"
    @onSubmit="Save"
  >
    <div style="width: 400px">
      Kami informasikan kepada pengguna bahwa terdapat indikasi akses tidak sah
      terhadap sistem elektronik.
      <br />
      <br />
      Saat ini sistem elektronik dalam keadaan normal dan tetap dapat melayani
      anda sebagaimana mestinya. Kami juga telah melakukan langkah-langkah untuk
      meningkatkan keamanan sebagai bentuk komitmen kami dalam menjaga
      integritas informasi Anda.
      <br />
      <br />
      <PERSON><PERSON><PERSON>, kami juga menghimbau agar Anda selalu melakukan penggantian kata
      sandi secara berkala untuk mencegah potensi penyalahgunaan data.
      Terimakasih.
    </div>
  </Modal>
</template>
<script>
import {
  startRegistration,
  // startAuthentication,
  browserSupportsWebAuthn,
} from '@simplewebauthn/browser'
export default {
  data: () => ({
    xshow: false,
    isFido2Loading: false,
    fido2Supported: false,
  }),
  props: {
    show: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  mounted() {
    // Check FIDO2/WebAuthn support
    this.fido2Supported = browserSupportsWebAuthn()
    if (!this.fido2Supported) {
      console.warn('WebAuthn is not supported in this browser')
    }
  },
  methods: {
    async Save() {
      if (!this.fido2Supported) {
        this.$api.notify('Browser tidak mendukung WebAuthn', 'error')
        return
      }

      this.isFido2Loading = true

      try {
        // Get registration options from server
        const optionsResponse = await this.$api.post(
          '/api/fido2/register/begin'
        )

        if (!optionsResponse.success) {
          this.$api.notify(
            optionsResponse.message || 'Gagal memulai registrasi',
            'error'
          )
          return
        }

        // Start registration with the browser
        const credential = await startRegistration(optionsResponse.data)

        // Send credential to server for verification
        const verificationResponse = await this.$api.post(
          '/api/fido2/register/complete',
          {
            credential,
          }
        )

        if (verificationResponse.success) {
          this.$api.notify('Biometrik berhasil didaftarkan!', 'success')
        } else {
          this.$api.notify(
            verificationResponse.message || 'Gagal mendaftarkan biometrik',
            'error'
          )
        }
      } catch (error) {
        console.error('FIDO2 registration error:', error)
        if (error.name === 'NotAllowedError') {
          this.$api.notify(
            'Registrasi dibatalkan atau tidak diizinkan',
            'error'
          )
        } else if (error.name === 'NotSupportedError') {
          this.$api.notify(
            'Perangkat tidak mendukung autentikasi biometrik',
            'error'
          )
        } else {
          this.$api.notify(
            'Gagal mendaftarkan biometrik: ' + error.message,
            'error'
          )
        }
      } finally {
        this.isFido2Loading = false
      }
    },
    // async Submit() {
    //   //   let ret = await this.api.call('', {})
    //   //   if (ret.success) this.emit('update:show', false)
    // },
  },
}
</script>
