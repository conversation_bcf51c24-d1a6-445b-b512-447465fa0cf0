// const axios = require('axios')
// const crypto = require('crypto')
// const moment = require('moment')
import axios from 'axios'
// import crypto from 'node:crypto'
import moment from 'moment'
import Vue from 'vue'
import helper from './helper'
import FingerprintJS from '@fingerprintjs/fingerprintjs'

let apiCache = {}
const API_URL = window.location.origin.replace(':8000', ':8001')
let DEVICE_ID = ''

const getDeviceId = async () => {
  const fp = await FingerprintJS.load()
  const { visitorId } = await fp.get()
  return visitorId
}

const api = axios.create({
  baseURL: API_URL,
  timeout: 9000000,
  withCredentials: true,
})
api.interceptors.request.use(
  async (config) => {
    const twj = localStorage.getItem('twj')
    if (twj) {
      config.headers.authorization = `Bearer ${twj}`
    }
    if (!DEVICE_ID) DEVICE_ID = await getDeviceId()
    config.headers['X-Device-Id'] = DEVICE_ID

    return config
  },
  (err) => Promise.reject(err)
)
const notify = function (message, type) {
  if (!type) type = 'success'
  Vue.$toast.open({ message: message, type: type, position: 'top' })
}
const toDropdown = function () {
  if (!this.length) return []
  var obj = this[0]
  var text, value
  for (var x in obj) {
    if (!value) value = x
    else if (!text) text = x
    else break
  }
  return this.map((d) => {
    return {
      ...d,
      text: d[text],
      value: d[value],
    }
  })
}
const toXML = function (array, spacey) {
  if (!array) return ''

  var sxml = ''
  for (var i = 0; i < array.length; i++) {
    sxml += '{row '
    for (let x in array[i]) {
      if (array[i][x] !== null) {
        if (spacey && array[i][x] && array[i][x].replace)
          sxml +=
            x +
            '="' +
            (array[i][x].replace
              ? array[i][x].replace(/\s/g, spacey)
              : array[i][x]) +
            '" '
        else
          sxml +=
            x +
            '="' +
            (typeof array[i][x] == 'boolean'
              ? array[i][x]
                ? 1
                : 0
              : array[i][x]) +
            '" '
      }
    }
    sxml += '/}'
  }
  return sxml
}
let queue = []
// let cache = {}
const sleep = (ms) => new Promise((res) => setTimeout(res, ms))
function regQueue(sp) {
  let id = sp + moment().valueOf()
  queue.push(id)
  // console.log(queue)
  setTimeout(() => {
    var idx = queue.indexOf(id)
    if (idx !== -1) {
      queue.splice(idx, 1)
    }
  }, 20000)
  return id
}
async function waitForQueue(id) {
  let to = 0
  while (queue.length && queue[0] != id) {
    await sleep(500)
    console.log(`wait ${id}:${queue.length}:${queue[0]}`)
    to++
    if (to > 40) {
      console.log(`timeout ${id}:${queue.length}`)
      var idx = queue.indexOf(id)
      if (idx !== -1) {
        queue.splice(idx, 1)
      }
      return 'Timeout Exceeded'
    }
  }
}
function releaseQueue(id) {
  // console.log(`release: ${id}`);
  var idx = queue.indexOf(id)
  if (idx !== -1) {
    queue.splice(idx, 1)
  }
}

function checkAccess(sp) {
  if (sp.match(/^EVO/)) {
    return true
  }

  let menu = localStorage.getItem('menu-access')
  if (!menu) {
    notify('Tidak memiliki akses', 'error')
    window.location = '/logout'
    return false
  }
  menu = JSON.parse(menu)
  let access = null
  for (let m in menu) {
    if (window.location.href.match(m)) {
      access = menu[m]
      break
    }
  }
  if (!access) {
    notify('Tidak memiliki akses', 'error')
    return false
  } else if (sp.match(/_(Sav|Upd|Ins)/) && !access.match(/w/)) {
    notify('Tidak memiliki akses', 'error')
    return false
  } else if (sp.match(/_(Del)/) && !access.match(/x/)) {
    notify('Tidak memiliki akses', 'error')
    return false
  }
  return true
}

let listeners = {}
function startListener(clientId) {
  const evtSource = new EventSource(API_URL + '/api/events/' + clientId, {
    withCredentials: true,
  })
  for (let t of ['notification', 'command']) {
    evtSource.addEventListener(t, (evt) => {
      if (listeners[t] && listeners[t].length) {
        for (let listener of listeners[t]) {
          if (evt.data) listener(JSON.parse(evt.data))
          else listener(evt.data)
        }
      }
    })
  }
}
if (localStorage.getItem('is-auth')) {
  startListener(localStorage.getItem('is-auth'))
}
// evtSource.addEventListener('system-update', (evt) => {
//   if (listeners['system-update'] && listeners['system-update'].length) {
//     for (let listener of listeners['system-update']) {
//       listener(evt)
//     }
//   }
// })

export default {
  url: API_URL,
  async login(params) {
    let { username, password, OTP } = params
    if (password)
      // password = crypto
      //   .createHash('md5')
      //   .update(params.password)
      //   .digest('hex')
      password = helper.md5(params.password)
    let { data } = await api.post('/api/login', {
      _Username: username,
      _Password: password,
      _OTP: OTP,
    })
    if (data.message) notify(data.message, data.success ? 'success' : 'error')
    if (data.token) {
      let clientId = data.token.substr(0, 8)
      localStorage.setItem('is-auth', data.token.substr(-8))
      if (window.location.hostname == 'localhost')
        localStorage.setItem('twj', data.token)
      startListener(clientId)
    }
    return data.data
  },
  logout() {
    localStorage.removeItem('is-auth')
  },
  async getOne(sp, params) {
    let d = await this.call(sp, params)
    if (d.data.length) return d.data[0]
    else return {}
  },
  async save(table, params, opts = {}) {
    let ret = await api.post(`/api/save/${table}`, params)
    if (ret.message) {
      if (!opts.silent) notify(ret.message, ret.success ? 'success' : 'error')
      if (ret.message.match(/Not authorized/))
        window.location = '/Main/App/Logout'
    }
  },
  async call(sp, params, opts = {}) {
    const { useCache } = opts
    sp = sp.replace('.', '_')

    let qid = null
    if (sp.match(/_(Sav|Upd|Ins|Del)/)) {
      if (!checkAccess(sp)) {
        return { success: false }
      }
      qid = regQueue(sp)
      let err = await waitForQueue(qid)
      if (err) {
        notify('Coba Lagi', 'error')
        return { success: false }
      }
    }

    // if (useCache) {
    if (apiCache[sp + JSON.stringify(params)]) {
      let { data } = await apiCache[sp + JSON.stringify(params)]
      return data
    }
    // }

    let newParams = undefined
    if (params) {
      newParams = JSON.parse(JSON.stringify(params))
      for (var x in newParams) {
        if (typeof newParams[x] == 'object') {
          // if (newParams[x] && newParams[x].type == 'Buffer') {
          //   newParams[x] = newParams[x][0] === 1
          // } else
          if (x.match(/^_?Json/)) {
            newParams[x] = JSON.stringify(newParams[x])
          } else if (newParams[x] && newParams[x].length !== undefined) {
            newParams[x] = toXML(newParams[x])
          }
        } else if (x.match(/Password/) && params[x].length < 32) {
          if (newParams[x].length < 8 && x !== 'OldPassword') {
            notify('Password minimal 8 karakter', 'error')
            return false
          } else if (
            x !== 'OldPassword' &&
            (!newParams[x].match(/[A-Z]/) ||
              !newParams[x].match(/[0-9]/) ||
              !newParams[x].match(/[!@#$%^&*()-+=-]/))
          ) {
            notify(
              'Password harus ada huruf besar, angka, dan karakter spesial',
              'error'
            )
            return false
          }
          // params[x] = crypto.createHash('md5').update(params[x]).digest('hex')
          newParams[x] = helper.md5(newParams[x])
        }
      }
    }

    // for (var x in params) {
    //   if (typeof params[x] == 'object') {
    //     if (params[x] && params[x].type == 'Buffer') {
    //       params[x] = params[x][0] === 1
    //     } else if (x.match(/^_?Json/)) {
    //       params[x] = JSON.stringify(params[x])
    //     } else if (params[x] && params[x].length !== undefined) {
    //       params[x] = toXML(params[x])
    //     }
    //   } else if (x.match(/Password/) && params[x].length < 32) {
    //     if (params[x].length < 8) {
    //       notify('Minimal 8 karakter', 'error')
    //       return false
    //     } else if (
    //       x !== 'OldPassword' &&
    //       (!params[x].match(/[A-Z]/) ||
    //         !params[x].match(/[0-9]/) ||
    //         !params[x].match(/[!@#$%^&*()-+=-]/))
    //     ) {
    //       notify('Harus ada huruf besar, angka, dan karakter spesial', 'error')
    //       return false
    //     }
    //     // params[x] = crypto.createHash('md5').update(params[x]).digest('hex')
    //     params[x] = helper.md5(params[x])
    //   }
    // }

    if (!params || JSON.stringify(params) == '{}') {
      let data = sessionStorage.getItem(sp)
      if (data) {
        data = JSON.parse(data)
        if (data.message) {
          if (!opts.silent)
            notify(data.message, data.success ? 'success' : 'error')
          if (data.message.match(/Not authorized/i))
            window.location = '/Main/App/Logout'
        }
        return data
      }
    }

    // console.log(params)
    // console.log(newParams)
    let retCall = api.post(`/api/call/${sp}`, newParams)
    // if (useCache) {
    apiCache[sp + JSON.stringify(params)] = retCall
    setTimeout(
      () => {
        apiCache[sp + JSON.stringify(params)] = null
      },
      useCache ? 30000 : 3000
    )
    // }
    let { data } = await retCall
    if (data.message == 'Server sedang penuh, coba beberapa saat lagi') {
      let xdata = await api.post(`/api/call/${sp}`, newParams)
      data = xdata.data
    }
    if (qid) releaseQueue(qid)
    if ((!params || JSON.stringify(params) == '{}') && data.success) {
      sessionStorage.setItem(sp, JSON.stringify(data))
    }
    if (data.message) {
      if (!opts.silent) notify(data.message, data.success ? 'success' : 'error')
      if (data.message.match(/Not authorized/))
        window.location = '/Main/App/Logout'
    }

    return data
  },
  async notify(message, mode) {
    notify(message, mode)
  },
  async select(obj, params) {
    let { data } = await api.post(`/api/select/${obj}`, params)
    if (data.message) notify(data.message, data.success ? 'success' : 'error')
    data.toDropdown = toDropdown.bind(data.data)
    return data
  },
  async upload(params) {
    let res = await api
      .post(`/api/upload`, params, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      .catch((err) => {
        console.error(err)
        notify('Upload Error', 'error')
      })
    if (res && res.data) {
      if (res.data.type == 'error') notify(res.data.message, 'error')
      else return res.data
    }
    return {}
  },
  async download(url) {
    var iframe = document.createElement('IFRAME')
    iframe.className = 'hide'
    iframe.src = url.match(/^\//)
      ? API_URL.replace(':8000', ':8001') + url
      : url
    document.body.appendChild(iframe)
    setTimeout(function () {
      iframe.remove()
    }, 10000)
  },
  async post(url, params, opts) {
    let { data } = await api.post(url, params, opts)
    if (opts) {
      if (opts.notification || opts.notify)
        notify(data.message, data.success ? 'success' : 'error')
    }
    return data
  },
  async get(url, opts) {
    let { data } = await api.get(url, opts)
    return data
  },
  async req(opts) {
    console.log(opts)
    let { data } = await api.request(opts)
    return data
  },
  async fromOutside(opts) {
    return axios(opts)
  },
  addEventListener(event, f) {
    if (!listeners[event]) listeners[event] = []
    listeners[event].push(f)
  },
}
