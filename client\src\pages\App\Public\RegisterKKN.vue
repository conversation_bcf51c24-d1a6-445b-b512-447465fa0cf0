<template>
  <v-container style="max-width: 100vw; padding: 0">
    <div class="login-box">
      <div
        style="
          height: 130px;
          background: url('/imgs/appex/banner.png');
          background-size: cover;
          background-position: center;
        "
      ></div>
      <div style="padding: 30px">
        <XInput
          label="No Handphone (Username)"
          :value.sync="forms.Username"
          class="inline"
          width="100%"
        />
        <div
          style="
            text-align: left;
            font-size: small;
            color: #888;
            margin-bottom: 10px;
          "
        >
          nomor ini akan digunakan sebagai user untuk masuk kedalam sistem
        </div>
        <XInput
          label="Password"
          :value.sync="forms.Password"
          type="password"
          width="calc(100% - 5px)"
        />

        <XInput
          label="Nama Lengkap"
          :value.sync="forms.FullName"
          width="100%"
        />
        <XInput label="NIM" :value.sync="forms.NIM" width="100%" />
        <XSelect
          label="Universitas"
          :value.sync="forms.University"
          :items="universities"
          width="calc(100% - 27px)"
          style="margin-bottom: 15px"
        />
        <XInput label="Kelompok" :value.sync="forms.Team" width="100%" />
        <XInput label="OTP" :value.sync="forms.OTP" width="100%" />
        <v-btn
          text
          small
          color="primary"
          @click="sendOTP"
          style="width: calc(100% - 40px)"
        >
          KIRIM OTP
        </v-btn>
        <br />
        <br />
        <v-btn
          color="primary"
          @click="submit"
          style="width: calc(100% - 40px)"
          :loading="loading"
        >
          DAFTAR
        </v-btn>
      </div>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
export default {
  components: {},
  data: () => ({
    forms: {
      username: '',
      password: '',
    },
    loading: false,
    showNews: false,
    universities: [
      { val: 'I-JIN SAIZU', txt: 'I-JIN SAIZU' },
      { val: 'POLINES SEMARANG', txt: 'POLINES SEMARANG' },
      { val: 'POLITEKNIK BANJARNEGARA', txt: 'POLITEKNIK BANJARNEGARA' },
      {
        val: 'STAI AL-ANWAR SARANG REMBANG',
        txt: 'STAI AL-ANWAR SARANG REMBANG',
      },
      { val: 'STAI TANBIHUL GHOFILIN', txt: 'STAI TANBIHUL GHOFILIN' },
      { val: 'STIKES IBNU SINA', txt: 'STIKES IBNU SINA' },
      { val: 'UDINUS', txt: 'UDINUS' },
      {
        val: 'UIN RADEN MAS SAID SURAKARTA',
        txt: 'UIN RADEN MAS SAID SURAKARTA',
      },
      { val: 'UIN SALATIGA', txt: 'UIN SALATIGA' },
      { val: 'UMNU KEBUMEN', txt: 'UMNU KEBUMEN' },
      { val: 'UNDIP', txt: 'UNDIP' },
      { val: 'UNIKA', txt: 'UNIKA' },
      { val: 'UNIMUS', txt: 'UNIMUS' },
      { val: 'UNISSULA', txt: 'UNISSULA' },
      { val: 'UNIV SAFIN PATI', txt: 'UNIV SAFIN PATI' },
      {
        val: 'UNIVERSITAS MUHAMMADIYAH PURWOKERTO',
        txt: 'UNIVERSITAS MUHAMMADIYAH PURWOKERTO',
      },
      {
        val: 'UNIVERSITAS MUHAMMADIYAH PURWOREJO',
        txt: 'UNIVERSITAS MUHAMMADIYAH PURWOREJO',
      },
      { val: 'UNIVERSITAS MURIA KUDUS', txt: 'UNIVERSITAS MURIA KUDUS' },
      { val: 'UNIVERSITAS PERADABAN', txt: 'UNIVERSITAS PERADABAN' },
      {
        val: 'UNIVERSITAS SLAMET RIYADI SURAKARTA',
        txt: 'UNIVERSITAS SLAMET RIYADI SURAKARTA',
      },
      { val: 'UNNES', txt: 'UNNES' },
      { val: 'UNPAND', txt: 'UNPAND' },
      { val: 'UNS', txt: 'UNS' },
      { val: 'UNSIQ WONOSOBO', txt: 'UNSIQ WONOSOBO' },
      { val: 'UNSOED', txt: 'UNSOED' },
      { val: 'UNTAG', txt: 'UNTAG' },
      { val: 'UNTIDAR', txt: 'UNTIDAR' },
      { val: 'UNWAHAS', txt: 'UNWAHAS' },
      { val: 'UNWIKU', txt: 'UNWIKU' },
      { val: 'UPGRIS', txt: 'UPGRIS' },
    ],
  }),
  mounted() {
    localStorage.clear()
    sessionStorage.clear()
    this.setMenu(null)
  },
  methods: {
    ...mapActions(['setMenu', 'setUser', 'setIgahpUser']),
    getListMenu(menu) {
      let mlist = {}
      menu.forEach((m) => {
        if (m.child && m.child.length) {
          if (m.MenuUrl) {
            mlist[m.MenuUrl] = this.getListMenu(m.child)
          } else mlist = Object.assign(mlist, this.getListMenu(m.child))
        } else if (m.MenuUrl) mlist[m.MenuUrl] = m.RWX
      })
      return mlist
    },
    async sendOTP() {
      let d = await this.$api.post(
        '/api/send-otp',
        {
          _Phone: this.forms.Username,
          _Purpose: 'reg-kkn',
        },
        { notify: true }
      )
    },
    async submit() {
      this.loading = true
      let d = await this.$api.call('EVO.SavUserKKN', this.forms)
      this.loading = false
      if (d.success) {
        this.$router.push('/login')
      }
    },
  },
}
</script>
<style lang="scss">
// .theme--light.v-application {
//   background: #f3f3f3;
// }
.login-box {
  width: calc(100% - 40px);
  max-width: 400px;
  background: white;
  box-sizing: content-box;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin: auto;
  margin-top: 10%;
  text-align: center;
  overflow: hidden;
}
</style>
