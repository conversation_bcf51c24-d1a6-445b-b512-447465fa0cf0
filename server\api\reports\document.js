const express = require('express')
const axios = require('axios')
const router = express.Router()
const db = require('../../common/db')
const {signPDF} = require('./pdf-signer')
const {authMiddleware} = require('../auth')
const {convertPDFToImages} = require('../../common/pdf2img')

router.post('/notify-new', authMiddleware, async (req, res) => {
  let sm = await db.query(`select sm.* from doc_suratmasuk sm
        WHERE sm.SuratID = ${req.body.SuratID}
        `)

  // let {to, message} = req.body
  let sId = 0
  for (let s of sm) {
    let surat = ''
    if (s.SuratUrl) {
      surat = `\n\nhttp://simperum.disperakim.jatengprov.go.id${s.SuratUrl}`
    }
    let link = `http://simperum.disperakim.jatengprov.go.id/Main/Dokumen/Dokumen?id=${s.SuratID}`
    let msg = `SURAT MASUK:\n${s.Perihal}\n${s.<PERSON>}${surat}\n\n${link}`
    let d = await axios.post(`http://silakon.dpubinmarcipka.jatengprov.go.id:2984/send`, {
      to: '6285777770060',
      message: msg,
      key: 'er1ganteng',
    })
    if (sId != s.SuratID) {
      sId = s.SuratID
    }
  }
  res.send({success: true})
})

router.get('/notify', async (req, res) => {
  let ip = (
    req.headers['cf-connecting-ip'] ||
    req.headers['x-real-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress || ''
  )
  console.log('notify', ip)
  let sm = await db.query(`select sm.*, au.FullName, au.Phone from doc_suratmasuk sm
            JOIN arch_user au 
            ON FIND_IN_SET(au.UserID, sm.Disposisi)
        WHERE sm.NotifyAt IS NULL`)

  // let {to, message} = req.body
  let sId = 0
  for (let s of sm) {
    let surat = ''
    if (s.SuratUrl) {
      surat = `\n\nhttp://simperum.disperakim.jatengprov.go.id${s.SuratUrl}`
    }
    let msg = `DISPOSISI:\n${s.Perihal}\n${s.Keterangan}\n\n${s.Catatan || '-'}${surat}`
    let d = await axios.post(`http://silakon.dpubinmarcipka.jatengprov.go.id:2984/send`, {
      to: s.Phone.replace('+', ''),
      message: msg,
      key: 'er1ganteng',
    })
    if (sId != s.SuratID) {
      sId = s.SuratID
      await db.query(`UPDATE doc_suratmasuk SET NotifyAt = NOW() WHERE SuratID = ${s.SuratID}`)
    }
  }
  res.send({success: true})
})

router.post('/sign', authMiddleware, async (req, res) => {
  const {LaporanUrl} = req.body
  const outfile = await signPDF(LaporanUrl)

  res.send({success: true, signedUrl: outfile})
})

router.post('/parse', authMiddleware, async (req, res) => {
  const {SuratUrl} = req.body

  const outImgs = await convertPDFToImages(SuratUrl)

  // const outImgs = ['27075001797.pdf_1.png', '27075001797.pdf_2.png'] 
  const headers = {
    "Authorization": "Bearer 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",
    // "HTTP-Referer": "simperum", // Optional. Site URL for rankings on openrouter.ai.
    "X-Title": "simperum", // Optional. Site title for rankings on openrouter.ai.
    // "Content-Type": "application/json"
  }

  let result = null
  for (let i = 0; i < outImgs.length; i++) {
    const img = outImgs[i]

    let d = await axios.post(`https://openrouter.ai/api/v1/chat/completions`, {
      "model": "meta-llama/llama-4-scout:free",
      "messages": [
        {
          "role": "user",
          "content": [
            {"type":"text","text":"Summarize the following images and get NoSurat, TglSurat, Asal (asal surat), Perihal, and Keterangan (keterangan acara) from the image. Return the result in JSON format, just return the JSON."},
            {"type":"image_url","image_url":{"url":"https://simperum.disperakim.jatengprov.go.id/reports/get/" + img}},
          ]
        }
      ]
    }, {headers})

    try {
      const content = d.data?.choices?.[0].message.content.trim();
      const cleanedContent = content.replace(/```json\n?|\n?```/g, '').replace(/\\n/g, '').replace(/\\"/g, '"');
      const parsedData = JSON.parse(cleanedContent);
      if(!result) {
        result = parsedData
      } else {
        result.NoSurat = result.NoSurat || parsedData.NoSurat
        result.TglSurat = result.TglSurat || parsedData.TglSurat
        result.Asal = result.Asal || parsedData.Asal
        result.Perihal = result.Perihal || parsedData.Perihal
        result.Keterangan = result.Keterangan || parsedData.Keterangan
      }
      if (result.NoSurat && result.TglSurat && result.Asal && result.Perihal) {
        break
      }
    } catch (error) {
      console.error('Error parsing JSON:', error);
    }
  }
  res.send({success: true, data: result});
})

module.exports = router